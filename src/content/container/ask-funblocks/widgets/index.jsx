import { findClosestNodeWithAttributes, findClosestNodeWithSelector, getDomain, getDrafterInfo, getSender, getTargetEditor, isFacebook, isGitHub, isGmail, isHotmail, isLinkedIn, isProductHunt, isReddit, isTwitter, isYoutube } from "@/content/utils/content-dom";
import { useCallback, useEffect, useState } from "react"

import { createRoot } from 'react-dom/client'
import Browser from "webextension-polyfill";
import { useSelectionManager } from "../../store/selection";
import { useView } from "../../store/view";
import { useReply } from "./reply";
import { useAIWriterButton } from "./ai-edit-button";
import { useIntl } from "react-intl";
import { useSummary } from "./video-summary";
import { isWidgetClosed } from "./close-widget-modal";
import { useSettings } from "@/common/store/settings";
import { useDispatch, useSelector } from "react-redux";
import { CLOSE_WIDGET_DIALOG, STRUCTURED_GENERATED_CONTENT } from "@/common/constants/actionTypes";
import { writerButtonElementId } from "./ai-edit-button";
import { useCodesExplain } from "./codes-explain";
import { funblocks_domain } from "@/common/serverAPIUtil";
import { callAIAction } from "@/common/actions/ticketAction";
import { extractJSONFromString } from "@/common/jsonStringUtil";
import { ProductFeatures } from "@/common/product-features";

const hasCodeAncestor = (node) => {
    let parentNode = node.parentNode;

    while (parentNode) {
        if (parentNode.tagName?.toLowerCase() === 'code') {
            return true;
        }
        parentNode = parentNode.parentNode;
    }

    return false;
}

const Widgets = () => {
    const selection = useSelectionManager();
    const [ai_worker, set_ai_worker] = useState();
    const { goToInputPage, openAssistantForSummary, openReaderAssistant, openWriterAssistantForEditor, retrieveTranscript } = useView();
    const { replyElementId, ReplyWidget, getHookDomForReply, getTargetEditorForReply, getMessageBodyDom } = useReply();
    const { getHookDomForSummary, SummaryWidget, summaryElementId } = useSummary();
    const { explainElementId, CodesExplainWidget } = useCodesExplain();
    const { writerButtonMirrorElementId, AIWriterButtonWidget } = useAIWriterButton();
    const [targetEditor, setTargetEditor] = useState();
    const intl = useIntl();
    const [loading, setLoading] = useState();
    const [close_widget_modal_state, set_close_widget_modal_state] = useState();
    const { settings } = useSettings()
    const dispatch = useDispatch();
    const closeWidgetModalState = useSelector(state => state.uiState.closeWidgetDialog) || {};
    const [errCode, setErrCode] = useState();

    //For widgets that show after document loaded
    useEffect(() => {
        if (!ProductFeatures.isWidgetEnabled('reply') || (!isHotmail && !isGmail && !isReddit && !isLinkedIn && !isTwitter && !isFacebook && !isProductHunt)) {
            return;
        }

        const observer = new MutationObserver((mutations) => {
            if (isLinkedIn && window.location.pathname.includes('feed') || isFacebook && !document.querySelector('[role="dialog"]')) {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE && !node.closest('.comments-thread-entity')) {
                                checkDOM(node);
                            }
                        });

                        mutation.removedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE && node.classList.contains('comments-comment-box__submit-button--cr')) {
                                removeWidget('div', replyElementId);
                            }
                        });
                    }
                });
            } else {
                checkDOM();
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
        });

        const checkDOM = (node) => {
            let hookDom = getHookDomForReply(node);

            if (hookDom && !hookDom.querySelector(`div[id="${replyElementId}"]`)) {
                const container = document.createElement('div');
                container.id = replyElementId;
                container.style.alignSelf = 'center';
                if (isProductHunt || isLinkedIn) {
                    hookDom.prepend(container);
                } else if (isTwitter) {
                    hookDom.insertBefore(container, hookDom.lastChild);
                } else {
                    hookDom.appendChild(container);
                }

                if (isTwitter) {
                    container.style.marginTop = '8px';
                } else if (isLinkedIn && window.location.pathname.includes('article')) {
                    hookDom.style.alignItems = 'center';
                    hookDom.style.columnGap = '20px';
                } else if (isLinkedIn && hookDom.querySelector('.comments-comment-box__submit-button--cr')) {
                    container.style.marginBottom = '0.86em';
                } else if (isFacebook) {
                    container.style.marginRight = '6px';
                }

                createRoot(container).render(<ReplyWidget label={{
                    read: intl.formatMessage({ id: 'ai_read' }),
                    reply: intl.formatMessage({ id: (isGmail || isHotmail) ? 'ai_reply' : 'ai_comment' }),
                    draft: intl.formatMessage({ id: 'ai_draft' })
                }} />)
            }
        }

        // 初始检查
        checkDOM(document.body);

        return () => {
            observer.disconnect();
        };
    }, [intl]);

    //for video summary
    useEffect(() => {
        if (!ProductFeatures.isWidgetEnabled('youtube') || !isYoutube) {
            return;
        }

        const observer = new MutationObserver(() => {
            checkDOM();
        });

        observer.observe(document, {
            childList: true,
            subtree: true,
        });

        const checkDOM = () => {
            let hookDom = getHookDomForSummary();

            if (hookDom && !hookDom?.querySelector(`div[id="${summaryElementId}"]`)) {
                const container = document.createElement('div');
                container.id = summaryElementId;
                container.style.zIndex = 2147483647;
                // container.style.pointerEvents = 'none';

                hookDom.prepend(container);
                createRoot(container).render(<SummaryWidget
                    summary_label={intl.formatMessage({ id: 'summarize_video' })}
                    video_assistant_label={intl.formatMessage({ id: 'video_assistant' })}
                    launch_ril_tooltip={intl.formatMessage({ id: 'launch_reading_assistant_tooltip' })}
                    launch_mindmap_tooltip={intl.formatMessage({ id: 'launch_mindmap_tooltip' })}
                    launch_summary_tooltip={intl.formatMessage({ id: 'launch_summary_tooltip' })}
                />)
            }
        }

        return () => {
            observer.disconnect();
        };
    }, [intl]);

    useEffect(() => {
        const existingVideoWidget = document.querySelector(`div[id="${summaryElementId}"]`);
        if (!existingVideoWidget) {
            return;
        }

        existingVideoWidget.parentNode.removeChild(existingVideoWidget);

        let hookDom = getHookDomForSummary();
        const container = document.createElement('div');
        container.id = summaryElementId;
        hookDom.prepend(container);
        createRoot(container).render(<SummaryWidget
            err_msg={errCode && intl.formatMessage({ id: errCode })} loading={loading}
            summary_label={intl.formatMessage({ id: 'summarize_video' })}
            video_assistant_label={intl.formatMessage({ id: 'video_assistant' })}
            launch_ril_tooltip={intl.formatMessage({ id: 'launch_reading_assistant_tooltip' })}
            launch_mindmap_tooltip={intl.formatMessage({ id: 'launch_mindmap_tooltip' })}
            launch_summary_tooltip={intl.formatMessage({ id: 'launch_summary_tooltip' })}
        />)
    }, [loading, errCode])

    //for code explain
    useEffect(() => {
        if (!ProductFeatures.isWidgetEnabled('codesExplain')) {
            return;
        }

        const observer = new MutationObserver(() => {
            checkDOM();
        });

        observer.observe(document, {
            childList: true,
            subtree: true,
        });

        const checkDOM = () => {
            Array.from(document.querySelectorAll('code'))
                .filter(block => {
                    return !hasCodeAncestor(block) && block.innerText?.split(/\n|;/).length > 3
                })
                .map((block, index) => {
                    let parent = block.parentNode;

                    const tagName = ProductFeatures.getProductId('codes-explain');
                    if (!parent.querySelector(`${tagName}[id^="${explainElementId}"]`)) {
                        const container = document.createElement(tagName);
                        container.id = explainElementId + '_' + index;
                        parent.prepend(container);
                        createRoot(container).render(<CodesExplainWidget label={intl.formatMessage({ id: 'explain' })} />)
                    }
                });

            if (isGitHub) {
                let raw_edit = document.querySelector('div.react-blob-header-edit-and-raw-actions')
                let codes = document.querySelector('textarea#read-only-cursor-text-area')

                if (raw_edit && codes) {
                    let parent = raw_edit.parentNode;
                    if (!parent.querySelector(`div[id^="${explainElementId}"]`)) {
                        const container = document.createElement('div');
                        container.id = explainElementId;
                        parent.prepend(container);
                        createRoot(container).render(<CodesExplainWidget label={intl.formatMessage({ id: 'explain' })} />)
                    }
                }
            }
        }

        return () => {
            observer.disconnect();
        };
    }, [intl]);

    const handleMessage = useCallback(async (event) => {
        if (!event?.data) {
            return;
        }

        const { type, data } = event.data;
        if (['launch_reply'].includes(type)) {
            set_ai_worker({
                type,
                id: Math.random() * 10000000
            })
        } else if (type === 'launch_ril') {
            setLoading({ operation: 'launch_ril' })
            openReaderAssistant(() => { setLoading(null) });
        } else if (type === 'launch_email_read') {
            // setLoading({ operation: 'launch_email_read' })
            // openReaderAssistant(() => { setLoading(null) });
            const sender = getSender() || '';
            // let content = selection.extractMeaningfulContent(getMessageBodyDom(), 10);
            // console.log('content.........',getMessageBodyDom(), content)
            selection.setSelectedContent({
                text: getMessageBodyDom()?.textContent,
                sender,
                type: 'email',
                isEditable: false
            })

            goToInputPage({ objType: 'ril', action: 'read_email' })
        } else if (type === 'launch_summary') {
            if (!loading) {
                setLoading({ operation: 'launch_summary' })
                openAssistantForSummary((errCode) => {
                    setLoading(null)
                    if (errCode) {
                        setErrCode(errCode)
                    }
                })

                // selection.setSelectedContent({
                //     url: window.location.href,
                //     type: 'video',
                //     isEditable: false
                // })
                // goToInputPage({ objType: 'ril', action: 'summary' })

            }
        } else if (type === 'launch_video_mindmap') {
            if (!loading) {
                setLoading({ operation: 'launch_video_mindmap' })

                retrieveTranscript((subtitles) => {
                    setLoading(null)
                    if (subtitles && subtitles.length > 0) {
                        selection.setSelectedContent({
                            text: `## Youtube video title: \n\n${document.title}\n\n## Youtube video transcript: \n\n${subtitles.join(' ')}`,
                            type: 'video',
                            isEditable: false
                        })

                        goToInputPage({ objType: 'ril', action: 'flow_mindmap' })
                    } else {
                        setErrCode('err_video_no_subtitles')
                    }
                }, () => {
                    setLoading(null)
                    setErrCode('err_failed_get_subtitles')
                })
            }
        } else if (type === 'launch_ai_write_assistant') {
            const editor = getTargetEditorForReply();

            openWriterAssistantForEditor(targetEditor || editor, '', 'assistant');
        } else if (type === 'launch_ai_composer') {
            const editor = getTargetEditorForReply();

            openWriterAssistantForEditor(targetEditor || editor, '', 'composer');
        } else if (type === 'launch_codes_explain') {
            selection.setSelectedContent({
                text: data.text,
                type: 'codes',
                isEditable: false
            })
            goToInputPage({ objType: 'ril', action: 'explain_codes' })
        } else if (['launch_switch_widget_modal'].includes(type)) {
            // set_close_widget_modal_state({ widget: data?.widget, open: true })
            dispatch({
                type: CLOSE_WIDGET_DIALOG,
                value: {
                    isModalOpen: true,
                    widget: data?.widget
                }
            })
        }
    }, [targetEditor, loading, openAssistantForSummary, openReaderAssistant]);

    const removeWidget = (tagName, widgetId) => {
        let existingWidget = document.querySelector(`${tagName}[id="${widgetId}"]`);

        if (existingWidget?.parentNode) {
            existingWidget.parentNode.removeChild(existingWidget);
        }
    }

    const attachAIWriterWidget = useCallback((targetElement) => {
        if (!ProductFeatures.isWidgetEnabled('aiWriter')) {
            return;
        }

        const isEditableElement =
            targetElement?.tagName === 'TEXTAREA' ||
            targetElement?.isContentEditable;

        if (isEditableElement) {
            if (isWidgetClosed(writerButtonElementId, settings, close_widget_modal_state)) {
                removeWidget('span', writerButtonElementId);
                return;
            }

            if (!targetElement.parentNode?.querySelector(`span[id="${writerButtonElementId}"]`)) {
                removeWidget('span', writerButtonElementId);

                const container = document.createElement('span');
                container.id = writerButtonElementId;
                container.style.position = 'absolute';
                container.style.left = '0px';
                container.style.top = '0px';
                container.style.zIndex = 2147483647;
                container.style.pointerEvents = 'none';
                if (isFacebook || isTwitter) {
                    targetElement.parentNode.style.height = isTwitter ? '28px' : '30px';
                    container.style.bottom = '0px';
                } else {
                    container.style.top = '0px';
                }
                targetElement.parentNode.appendChild(container);
                createRoot(container).render(<AIWriterButtonWidget targetEditor={targetElement} />)

                setTargetEditor(targetElement);
            }
        }

    }, [settings, close_widget_modal_state]);

    //for widgets show when focused...
    const handleFocusChange = useCallback(() => {
        attachAIWriterWidget(document.activeElement);
    }, [attachAIWriterWidget]);

    const handleKeyPress = useCallback((event) => {
        attachAIWriterWidget(event.target)
    }, [attachAIWriterWidget])

    useEffect(() => {
        window.addEventListener('message', handleMessage);

        if (ProductFeatures.isWriteAssistantEnabled()) {
            document.addEventListener('focus', handleFocusChange, true);
            document.addEventListener('keypress', handleKeyPress)
        }

        return () => {
            window.removeEventListener('message', handleMessage);
            if (ProductFeatures.isWriteAssistantEnabled()) {
                document.removeEventListener('focus', handleFocusChange, true);
                document.removeEventListener('keypress', handleKeyPress);
            }

        };
    }, [handleMessage, handleFocusChange, handleKeyPress]);

    useEffect(() => {
        const rePosition = () => {
            let writer_widget = document.querySelector(`div[id="${writerButtonMirrorElementId}"]`);
            if (!writer_widget) {
                return;
            }

            // console.log('rect........', targetEditor.getBoundingClientRect(), targetEditor, writer_widget.getBoundingClientRect(), writer_widget)
            writer_widget.style.left = targetEditor.getBoundingClientRect().x - writer_widget.getBoundingClientRect().x + 'px';
            writer_widget.style.top = targetEditor.getBoundingClientRect().y - writer_widget.getBoundingClientRect().y + 'px';
        }

        const resizeObserver = new ResizeObserver((entries) => {
            for (const entry of entries) {
                const { width, height, left, top } = entry.contentRect;
                let writer_widget = document.querySelector(`div[id="${writerButtonMirrorElementId}"]`);
                if (!writer_widget) {
                    return;
                }

                // console.log('entry rect.........', entry.contentRect, entry, targetEditor.getBoundingClientRect(), writer_widget.getBoundingClientRect())

                writer_widget.style.width = 2 * left + width + 'px';
                writer_widget.style.height = 2 * top + height + 'px';

                if (targetEditor.parentNode.getBoundingClientRect().x - writer_widget.getBoundingClientRect().x) {
                    writer_widget.style.left = left + targetEditor.getBoundingClientRect().x - writer_widget.getBoundingClientRect().x + 'px';
                }
                if (targetEditor.getBoundingClientRect().y - writer_widget.getBoundingClientRect().y) {
                    writer_widget.style.top = top + targetEditor.getBoundingClientRect().y - writer_widget.getBoundingClientRect().y + 'px';
                }
            }
        });

        if (targetEditor) {
            rePosition();
            resizeObserver.observe(targetEditor);
        }

        return () => {
            if (targetEditor) {
                resizeObserver.unobserve(targetEditor);
            }
        };
    }, [targetEditor]);

    useEffect(() => {
        if (!ai_worker?.id) return;

        if (isReddit || isTwitter || isLinkedIn || isFacebook || isProductHunt) {
            const editor = getTargetEditor();

            openWriterAssistantForEditor(targetEditor || editor, '', 'composer');
            return;
        }

        const observer = new MutationObserver(() => {
            launchEmailReply();
        });

        observer.observe(document, {
            childList: true,
            subtree: true,
        });

        const launchEmailReply = () => {
            const editor = getTargetEditorForReply()
            if (editor) {
                // setTimeout(() => {
                selection.select_from_start(editor, '');

                const { type } = ai_worker;
                if (type === 'launch_reply') {
                    const sender = getSender() || '';
                    let content = getMessageBodyDom()?.textContent;
                    selection.setSelectedContent({
                        text: content,
                        sender,
                        type: 'email',
                        isEditable: true
                    })
                    goToInputPage({ objType: 'ril', action: 'reply_email' })
                }
                // }, 200)
                observer.disconnect();
            }
        };

        return () => {
            observer.disconnect();
        };
    }, [ai_worker?.id]);

    useEffect(() => {
        if (closeWidgetModalState?.isModalOpen || ![writerButtonElementId].includes(closeWidgetModalState.widget) || !closeWidgetModalState.data) {
            return;
        }

        set_close_widget_modal_state(closeWidgetModalState);
        removeWidget(closeWidgetModalState.widget === writerButtonElementId && 'span' || 'div', closeWidgetModalState.widget);

        dispatch({
            type: CLOSE_WIDGET_DIALOG,
            value: {
                isModalOpen: false
            }
        })

    }, [closeWidgetModalState])

    return null;
}

export default Widgets
