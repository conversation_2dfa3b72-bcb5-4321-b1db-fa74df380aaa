import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactCrop from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import browser from 'webextension-polyfill'
import { Screenshot } from '@styled-icons/boxicons-regular/Screenshot';
import { Check } from '@styled-icons/material/Check';
import { Close } from '@styled-icons/material/Close';
import { Download } from '@styled-icons/boxicons-regular/Download';
import { Tooltip } from 'antd';
import { useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import { OPERATION_FAILED, SCREEN_CAPTURE } from '@/common/constants/actionTypes';
import './screen-capture.css'
import { Magic } from '@styled-icons/bootstrap/Magic';
import { useView } from "../store/view";
import { Flowchart } from '@styled-icons/fluentui-system-regular/Flowchart';
import { addNote, getUserInfo, uploadImgs } from '@/common/actions/ticketAction';
import { funblocks_domain } from '@/common/serverAPIUtil';
import { ThreeDots } from 'react-loader-spinner'
import { Save } from '@styled-icons/boxicons-regular/Save';
import { extractWebContent } from '@/common/article_extractor';
import ConfirmMessage from '../ask-funblocks/confirm_message';
import { EventName } from '@/common/event-name';

const CropScreen = () => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const screen_capture = useSelector(state => state.uiState.screen_capture)
  const operationStatus = useSelector(state => state.operationStatus);
  const loginUser = useSelector((state) => state.loginIn && state.loginIn.user)

  const [srcImg, setSrcImg] = useState(null);
  const [crop, setCrop] = useState({ unit: '%', x: 0, y: -1, width: 1 });
  const [croppedImage, setCroppedImage] = useState();
  const [croppedBlob, setCroppedBlob] = useState();
  const [copied, setCopied] = useState();
  const [toolbarPosition, setToolbarPosition] = useState(null);
  const [doing, setDoing] = useState();
  const { openImageAssistant, hide } = useView();

  const [confirmMessage, setConfirmMessage] = useState();
  const [toast, setToast] = useState();

  const imgRef = useRef(null);

  useEffect(() => {
    dispatch(
      getUserInfo(
        {},
      )
    )
  }, [])

  useEffect(() => {
    if (!operationStatus?.inProgress) {
      setDoing(null);
    }

    if (operationStatus && !operationStatus.success) {
      showToast({
        msg: 'Operation failed, please try again later!',
        done: false
      })
    }
  }, [operationStatus])

  const showToast = useCallback((toast) => {
    setToast(toast)
    setTimeout(() => {
      setToast(null)
    }, 3000)
  }, [])

  const captureScreen = async () => {
    const res = await browser.runtime.sendMessage(
      {
        type: 'screenshot',
        format: 'png',
        quality: 1
      }
    );

    setSrcImg(res.image);
  };

  useEffect(() => {
    if (!screen_capture) return;

    if (screen_capture.shot) {
      dispatch({
        type: SCREEN_CAPTURE,
        value: {
          shot: false,
          crop: true
        }
      })

      setTimeout(() => captureScreen(), 100)
    }
  }, [screen_capture]);

  const cropImage = (completedCrop) => {
    if (!completedCrop.width || !completedCrop.height) {
      closeCrop();
      return;
    }

    const imgElement = imgRef.current;
    const scaleX = imgElement.naturalWidth / imgElement.width;
    const scaleY = imgElement.naturalHeight / imgElement.height;
    const cropPixels = {
      x: completedCrop.x * scaleX,
      y: completedCrop.y * scaleY,
      width: completedCrop.width * scaleX,
      height: completedCrop.height * scaleY,
    };

    const canvas = document.createElement('canvas');
    canvas.width = cropPixels.width;
    canvas.height = cropPixels.height;
    const ctx = canvas.getContext('2d');

    ctx.drawImage(
      imgElement,
      cropPixels.x,
      cropPixels.y,
      cropPixels.width,
      cropPixels.height,
      0,
      0,
      cropPixels.width,
      cropPixels.height
    );

    setCroppedImage(canvas.toDataURL('image/png'));
    canvas.toBlob(b => setCroppedBlob(b), 'image/png');

    const toolbarTop = completedCrop.y + completedCrop.height + 10; // 10px 的间距
    const adjustedTop = toolbarTop + 50 > window.innerHeight ? toolbarTop - 60 : toolbarTop; // Adjust if it exceeds the visible area
    setToolbarPosition({
      top: adjustedTop,
      right: window.innerWidth - completedCrop.x - completedCrop.width + 5
    });

  };

  const toAIFlow = useCallback(() => {
    if (!loginUser?._id) {
      return checkLogin()
    }

    setDoing('toAIFlow');
    dispatch(uploadImgs({ data: { files: [{ uri: croppedImage }] } }, (files) => {
      if (!files || !files.length || !files[0].uri) {
        return dispatch({
          type: OPERATION_FAILED,
          message: intl.formatMessage({ id: 'upload_failed' })
        });
      }

      // window.open(`http://localhost:3000/#/aiflow?action=initNode&nodeType=image&src=${files[0].uri}`, '_blank')
      window.open(`https://app.${funblocks_domain}/#/aiflow?action=initNode&nodeType=image&src=${files[0].uri}`, '_blank')
    }, 'screencapture'));
  }, [croppedImage, loginUser?._id]);

  const downloadImage = useCallback(() => {
    setDoing('downloadImage');
    const a = document.createElement('a');
    a.setAttribute('download', 'funblocks_screenshot.png');
    a.setAttribute('href', croppedImage);
    a.click();

    setDoing(null);
    closeScreenCapture();
  }, [croppedImage]);

  const checkLogin = useCallback(() => {
    return setConfirmMessage({
      content: intl.formatMessage({ id: 'feature_for_members' }),
      okText: intl.formatMessage({ id: 'settings' }),
      onConfirm: () => {
        browser.runtime.sendMessage({
          type: EventName.showOptions,
        })

        setConfirmMessage({
          content: intl.formatMessage({ id: 'confirm_logon' }),
          onConfirm: () =>
            dispatch(
              getUserInfo(
                {},
                () => {
                  setConfirmMessage(null)
                },
                () => {
                  setConfirmMessage(null)
                }
              )
            ),
        })
      },
    })
  }, []);

  const saveImage = useCallback(() => {
    if (!loginUser?._id) {
      return checkLogin()
    }

    setDoing('saveImage');

    dispatch(uploadImgs({ data: { files: [{ uri: croppedImage }] } }, (files) => {
      if (!files || !files.length || !files[0].uri) {
        return dispatch({
          type: OPERATION_FAILED,
          message: intl.formatMessage({ id: 'upload_failed' })
        });
      }

      saveToMemo(files[0].uri)
    }, 'screencapture'));
  }, [croppedImage, loginUser?._id])

  const saveToMemo = useCallback(
    (url) => {
      let article = extractWebContent()
      const title = article?.title
      let blocks = [{
        type: 'img',
        url,
        children: [
          {
            text: '',
          },
        ],
      }]

      let data = {
        title,
        content: blocks,
        url: window.location.href,
        // article_title: article?.title,
        article_content: article?.content,
      }

      dispatch(
        addNote(
          {
            data,
          },
          () => {
            showToast({
              msg: intl.formatMessage({ id: 'saved_to_memo' }),
              done: true
            })
          },
          'widget'
        )
      )
    }, [])

  const copyImage = useCallback(() => {
    navigator.clipboard.write([
      new ClipboardItem({
        [croppedBlob.type]: croppedBlob
      })
    ]).then(() => {
      setCopied(true)
      setTimeout(() => closeScreenCapture(), 1000)
    });
  }, [croppedBlob]);

  const closeCrop = useCallback(() => {
    setToolbarPosition(null)
    setCrop({ unit: '%', x: 0, y: -1, width: 1 });
    setCroppedImage(null);
    setCopied(false);
    hide()
  }, []);

  const closeScreenCapture = useCallback(() => {
    closeCrop();

    setSrcImg(null);
    dispatch({
      type: SCREEN_CAPTURE,
      value: {
        shot: false,
        crop: false
      }
    })
  }, []);

  if (!srcImg) return null;

  const loadingIcon = <ThreeDots
    height="8"
    width="18"
    radius="9"
    color='dodgerblue'
    ariaLabel='three-dots-loading'
  />

  return (<div
    style={{
      position: 'fixed',
      left: 0,
      top: 0,
      width: window.innerWidth,
      height: window.innerHeight,
      zIndex: 9999999
    }}
  >
    <ReactCrop
      crop={crop}
      onChange={(newCrop) => setCrop(newCrop)}
      onComplete={cropImage}
    >
      <img
        ref={imgRef}
        alt="Crop me"
        src={srcImg}
      />

    </ReactCrop>
    {
      !croppedImage &&
      <Tooltip
        title={intl.formatMessage({ id: 'close' })}
        placement='bottom'
      >
        <div
          className='hoverStand'
          style={{
            position: 'absolute',
            top: 10,
            right: 25,
            zIndex: 99999,
            padding: 5,
            borderRadius: 40,
            border: '2px solid white'
          }}
          onClick={() => {
            closeScreenCapture()
          }}
        >
          <Close size={30} color='white' />
        </div>
      </Tooltip>
    }
    {
      croppedImage &&
      <div
        style={{
          position: 'absolute',
          top: toolbarPosition?.top,
          right: toolbarPosition?.right,
          zIndex: 99999,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-end',
          rowGap: 8
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            backgroundColor: '#fff',
            padding: '2px',
            borderTop: '1px solid #ccc',
            borderRadius: 4,
            boxShadow: '0px 0px 8px #bbb',
            width: 'fit-content'
          }}
        >
          <Tooltip
            title={intl.formatMessage({ id: 'ask_ai' })}
            placement='bottom'
          >
            <div
              className='hoverStand'
              style={{
                padding: 5,
              }}
              onClick={() => {
                openImageAssistant(croppedImage)
              }}
            >
              <Magic size={17} color='dodgerblue' />
            </div>
          </Tooltip>

          <Tooltip
            title={intl.formatMessage({ id: 'ai_flow_image' })}
            placement='bottom'
          >
            <div
              className='hoverStand'
              style={{
                padding: 5,
              }}
              onClick={() => toAIFlow()}
            >
              {doing == 'toAIFlow' && loadingIcon}
              {doing != 'toAIFlow' && <Flowchart size={20} color='dodgerblue' />}
            </div>
          </Tooltip>

          <div
            style={{
              width: '100%',
              borderLeft: '1px solid #eee',
            }}
          />

          <Tooltip title={intl.formatMessage({ id: 'save_to_memo' })}>
            <div
              className="hoverStand"
              onClick={() => saveImage()}
            >
              {doing == 'saveImage' && loadingIcon}
              {doing != 'saveImage' && <Save size={20} />}
            </div>
          </Tooltip>
          <Tooltip
            title={intl.formatMessage({ id: 'download' })}
            placement='bottom'
          >
            <div
              className='hoverStand'
              style={{
                padding: 5,
              }}
              onClick={downloadImage}
            >
              {doing == 'downloadImage' && loadingIcon}
              {doing != 'downloadImage' && <Download size={20} />}
            </div>
          </Tooltip>
          <Tooltip
            title={intl.formatMessage({ id: 'close' })}
            placement='bottom'
          >
            <div
              className='hoverStand'
              style={{
                padding: 5
              }}
              onClick={() => closeScreenCapture()}
            >
              <Close size={20} />
            </div>
          </Tooltip>
          <Tooltip
            title={intl.formatMessage({ id: copied ? 'copied' : 'copy' })}
            placement='bottom'
          >
            <div
              className='hoverStand'
              style={{
                padding: 5
              }}
              onClick={copyImage}
            >
              <Check color={copied ? 'green' : undefined} size={20} />
            </div>
          </Tooltip>
        </div>

        {
          (confirmMessage || toast) &&
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: '6px',
              maxWidth: '300px',
              padding: '10px',
              boxShadow: '0px 0px 8px #bbb',
              transition: 'box-shadow 0.3s ease-in-out',
              backgroundColor: '#fafafa',
              color: '#333',
            }}
          >
            {
              confirmMessage &&
              < ConfirmMessage
                content={confirmMessage.content}
                okText={confirmMessage.okText}
                onCancel={() => setConfirmMessage(null)}
                onConfirm={confirmMessage.onConfirm}
                style={{
                  padding: 10,
                }}
              />
            }
            {
              toast &&
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  width: 'fit-content',
                }}
              >
                <div>{toast.msg}</div>
                {
                  toast.done &&
                  <Check size={18} color="green" />
                }
              </div>
            }
          </div>
        }
      </div>
    }
  </div>
  )
};

export default CropScreen;