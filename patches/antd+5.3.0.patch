diff --git a/node_modules/antd/es/tooltip/index.js b/node_modules/antd/es/tooltip/index.js
index fa4ab64..f10f447 100644
--- a/node_modules/antd/es/tooltip/index.js
+++ b/node_modules/antd/es/tooltip/index.js
@@ -246,7 +246,7 @@ const Tooltip = /*#__PURE__*/React.forwardRef((props, ref) => {
     }),
     motion: {
       motionName: getTransitionName(rootPrefixCls, 'zoom-big-fast', props.transitionName),
-      motionDeadline: 1000
+      motionDeadline: 10
     },
     destroyTooltipOnHide: !!destroyTooltipOnHide
   }), tempOpen ? cloneElement(child, {
