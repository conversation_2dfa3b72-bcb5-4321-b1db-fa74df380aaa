<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .log {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>PDF Content Extraction Debug Tool</h1>
    
    <div class="test-section">
        <h2>Page Information</h2>
        <div id="pageInfo" class="result"></div>
        <button onclick="checkPageInfo()">Check Page Info</button>
    </div>

    <div class="test-section">
        <h2>PDF Detection</h2>
        <div id="pdfDetection" class="result"></div>
        <button onclick="testPDFDetection()">Test PDF Detection</button>
    </div>

    <div class="test-section">
        <h2>DOM Elements</h2>
        <div id="domElements" class="result"></div>
        <button onclick="checkDOMElements()">Check DOM Elements</button>
    </div>

    <div class="test-section">
        <h2>Text Extraction Test</h2>
        <div id="textExtraction" class="result"></div>
        <button onclick="testTextExtraction()">Test Text Extraction</button>
    </div>

    <div class="test-section">
        <h2>Console Logs</h2>
        <div id="consoleLogs" class="log"></div>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>

    <script>
        // 捕获控制台日志
        const originalLog = console.log;
        const originalError = console.error;
        const logs = [];

        console.log = function(...args) {
            logs.push('[LOG] ' + args.join(' '));
            updateLogs();
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            logs.push('[ERROR] ' + args.join(' '));
            updateLogs();
            originalError.apply(console, args);
        };

        function updateLogs() {
            document.getElementById('consoleLogs').textContent = logs.slice(-20).join('\n');
        }

        function clearLogs() {
            logs.length = 0;
            updateLogs();
        }

        function checkPageInfo() {
            const info = {
                url: window.location.href,
                contentType: document.contentType,
                title: document.title,
                userAgent: navigator.userAgent,
                isFileProtocol: window.location.protocol === 'file:',
                hasExtension: !!window.chrome?.runtime?.id
            };
            
            document.getElementById('pageInfo').textContent = JSON.stringify(info, null, 2);
        }

        function testPDFDetection() {
            const isPDFPage = document.contentType === 'application/pdf' ||
                              window.location.href.toLowerCase().endsWith('.pdf') ||
                              document.querySelector('embed[type="application/pdf"]') ||
                              document.querySelector('object[type="application/pdf"]');

            const detection = {
                isPDFPage,
                contentType: document.contentType,
                urlEndsPdf: window.location.href.toLowerCase().endsWith('.pdf'),
                hasEmbedPdf: !!document.querySelector('embed[type="application/pdf"]'),
                hasObjectPdf: !!document.querySelector('object[type="application/pdf"]')
            };

            document.getElementById('pdfDetection').textContent = JSON.stringify(detection, null, 2);
        }

        function checkDOMElements() {
            const elements = {
                textLayers: document.querySelectorAll('.textLayer').length,
                pdfViewer: !!document.querySelector('#viewer'),
                pdfViewerClass: !!document.querySelector('.pdfViewer'),
                pageElements: document.querySelectorAll('.page').length,
                dataPageElements: document.querySelectorAll('[data-page-number]').length,
                canvasElements: document.querySelectorAll('canvas').length,
                embedElements: document.querySelectorAll('embed').length,
                objectElements: document.querySelectorAll('object').length
            };

            // 检查具体的选择器
            const selectors = [
                '.textLayer',
                '.page .textLayer div',
                '.pdfViewer .page .textLayer div',
                '[data-page-number] .textLayer div',
                '.react-pdf__Page__textContent div',
                '.rpv-core__text-layer div',
                '#pageContainer1 .textLayer div',
                '.canvasWrapper + .textLayer div'
            ];

            const selectorResults = {};
            selectors.forEach(selector => {
                selectorResults[selector] = document.querySelectorAll(selector).length;
            });

            const result = {
                elements,
                selectorResults
            };

            document.getElementById('domElements').textContent = JSON.stringify(result, null, 2);
        }

        async function testTextExtraction() {
            try {
                // 模拟扩展的文本提取逻辑
                let result = 'Testing text extraction...\n\n';

                // 检查 textLayer
                const textLayers = document.querySelectorAll('.textLayer');
                result += `Found ${textLayers.length} text layers\n`;

                if (textLayers.length > 0) {
                    let extractedText = '';
                    textLayers.forEach((layer, index) => {
                        const textDivs = layer.querySelectorAll('div, span');
                        result += `Layer ${index} has ${textDivs.length} text elements\n`;
                        textDivs.forEach(element => {
                            const text = element.textContent || element.innerText;
                            if (text && text.trim()) {
                                extractedText += text + ' ';
                            }
                        });
                    });
                    
                    if (extractedText.trim()) {
                        result += `\nExtracted text from textLayers:\n${extractedText.substring(0, 500)}...\n`;
                    }
                }

                // 检查页面文本
                const bodyText = document.body.innerText || document.body.textContent;
                if (bodyText && bodyText.trim().length > 50) {
                    result += `\nBody text length: ${bodyText.length}\n`;
                    result += `Body text preview:\n${bodyText.substring(0, 500)}...\n`;
                }

                document.getElementById('textExtraction').textContent = result;
            } catch (error) {
                document.getElementById('textExtraction').textContent = 'Error: ' + error.message;
            }
        }

        // 自动运行初始检查
        window.onload = function() {
            checkPageInfo();
            testPDFDetection();
            checkDOMElements();
        };
    </script>
</body>
</html>
